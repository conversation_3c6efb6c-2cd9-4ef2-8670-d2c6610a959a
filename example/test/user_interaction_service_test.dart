import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../lib/services/user_interaction_service.dart';
import '../lib/models/user_interaction.dart';
import '../lib/models/story.dart';

void main() {
  group('UserInteractionService Tests', () {
    late UserInteractionService service;
    late Story testStory;

    setUp(() async {
      // 设置测试环境
      SharedPreferences.setMockInitialValues({});
      service = await UserInteractionService.getInstance();

      // 清理之前的数据
      await service.clearAllLikes();
      await service.clearAllFavorites();

      testStory = Story(
        id: 'test_story',
        title: {'zh': '测试故事', 'en': 'Test Story'},
        backgroundSetting: {'zh': '测试背景', 'en': 'Test background'},
        characterSetting: {'zh': '测试角色', 'en': 'Test character'},
        currentPlot: {'zh': '测试剧情', 'en': 'Test plot'},
        imageUrl: 'assets/images/test.jpg',
        categoryId: 'test_category',
        popularity: 100,
      );
    });

    test('should initialize with empty lists', () {
      expect(service.getLikedStories(), isEmpty);
      expect(service.getFavoriteStories(), isEmpty);
      expect(service.getLikesCount(), equals(0));
      expect(service.getFavoritesCount(), equals(0));
    });

    test('should like a story', () async {
      expect(service.isStoryLiked(testStory.id), isFalse);
      
      await service.likeStory(testStory, 'zh');
      
      expect(service.isStoryLiked(testStory.id), isTrue);
      expect(service.getLikesCount(), equals(1));
      
      final likedStories = service.getLikedStories();
      expect(likedStories.length, equals(1));
      expect(likedStories.first.storyId, equals(testStory.id));
      expect(likedStories.first.type, equals(UserInteractionType.like));
    });

    test('should unlike a story', () async {
      // 先点赞
      await service.likeStory(testStory, 'zh');
      expect(service.isStoryLiked(testStory.id), isTrue);
      
      // 取消点赞
      await service.unlikeStory(testStory.id);
      expect(service.isStoryLiked(testStory.id), isFalse);
      expect(service.getLikesCount(), equals(0));
    });

    test('should favorite a story', () async {
      expect(service.isStoryFavorited(testStory.id), isFalse);
      
      await service.favoriteStory(testStory, 'zh');
      
      expect(service.isStoryFavorited(testStory.id), isTrue);
      expect(service.getFavoritesCount(), equals(1));
      
      final favoriteStories = service.getFavoriteStories();
      expect(favoriteStories.length, equals(1));
      expect(favoriteStories.first.storyId, equals(testStory.id));
      expect(favoriteStories.first.type, equals(UserInteractionType.favorite));
    });

    test('should unfavorite a story', () async {
      // 先收藏
      await service.favoriteStory(testStory, 'zh');
      expect(service.isStoryFavorited(testStory.id), isTrue);
      
      // 取消收藏
      await service.unfavoriteStory(testStory.id);
      expect(service.isStoryFavorited(testStory.id), isFalse);
      expect(service.getFavoritesCount(), equals(0));
    });

    test('should handle multiple stories', () async {
      final story2 = Story(
        id: 'test_story_2',
        title: {'zh': '测试故事2', 'en': 'Test Story 2'},
        backgroundSetting: {'zh': '测试背景2', 'en': 'Test background 2'},
        characterSetting: {'zh': '测试角色2', 'en': 'Test character 2'},
        currentPlot: {'zh': '测试剧情2', 'en': 'Test plot 2'},
        imageUrl: 'assets/images/test2.jpg',
        categoryId: 'test_category',
        popularity: 90,
      );

      // 点赞两个故事
      await service.likeStory(testStory, 'zh');
      await service.likeStory(story2, 'zh');
      
      expect(service.getLikesCount(), equals(2));
      expect(service.isStoryLiked(testStory.id), isTrue);
      expect(service.isStoryLiked(story2.id), isTrue);
      
      // 收藏一个故事
      await service.favoriteStory(testStory, 'zh');
      
      expect(service.getFavoritesCount(), equals(1));
      expect(service.isStoryFavorited(testStory.id), isTrue);
      expect(service.isStoryFavorited(story2.id), isFalse);
    });

    test('should maintain order (newest first)', () async {
      final story2 = Story(
        id: 'test_story_2',
        title: {'zh': '测试故事2', 'en': 'Test Story 2'},
        backgroundSetting: {'zh': '测试背景2', 'en': 'Test background 2'},
        characterSetting: {'zh': '测试角色2', 'en': 'Test character 2'},
        currentPlot: {'zh': '测试剧情2', 'en': 'Test plot 2'},
        imageUrl: 'assets/images/test2.jpg',
        categoryId: 'test_category',
        popularity: 90,
      );

      // 先点赞第一个故事
      await service.likeStory(testStory, 'zh');
      await Future.delayed(const Duration(milliseconds: 10));
      
      // 再点赞第二个故事
      await service.likeStory(story2, 'zh');
      
      final likedStories = service.getLikedStories();
      expect(likedStories.length, equals(2));
      // 最新的应该在前面
      expect(likedStories.first.storyId, equals(story2.id));
      expect(likedStories.last.storyId, equals(testStory.id));
    });

    test('should clear all likes', () async {
      await service.likeStory(testStory, 'zh');
      expect(service.getLikesCount(), equals(1));
      
      await service.clearAllLikes();
      expect(service.getLikesCount(), equals(0));
      expect(service.getLikedStories(), isEmpty);
    });

    test('should clear all favorites', () async {
      await service.favoriteStory(testStory, 'zh');
      expect(service.getFavoritesCount(), equals(1));
      
      await service.clearAllFavorites();
      expect(service.getFavoritesCount(), equals(0));
      expect(service.getFavoriteStories(), isEmpty);
    });

    test('should handle localized titles', () async {
      await service.likeStory(testStory, 'en');
      
      final likedStories = service.getLikedStories();
      expect(likedStories.first.storyTitle, equals('Test Story'));
      
      // 测试中文本地化
      await service.clearAllLikes();
      await service.likeStory(testStory, 'zh');
      
      final likedStoriesZh = service.getLikedStories();
      expect(likedStoriesZh.first.storyTitle, equals('测试故事'));
    });

    test('should not duplicate likes for same story', () async {
      await service.likeStory(testStory, 'zh');
      expect(service.getLikesCount(), equals(1));
      
      // 再次点赞同一个故事
      await service.likeStory(testStory, 'zh');
      expect(service.getLikesCount(), equals(1)); // 应该还是1个
      
      final likedStories = service.getLikedStories();
      expect(likedStories.length, equals(1));
    });

    test('should not duplicate favorites for same story', () async {
      await service.favoriteStory(testStory, 'zh');
      expect(service.getFavoritesCount(), equals(1));
      
      // 再次收藏同一个故事
      await service.favoriteStory(testStory, 'zh');
      expect(service.getFavoritesCount(), equals(1)); // 应该还是1个
      
      final favoriteStories = service.getFavoriteStories();
      expect(favoriteStories.length, equals(1));
    });
  });

  group('UserInteraction Model Tests', () {
    test('should create UserInteraction correctly', () {
      final interaction = UserInteraction(
        storyId: 'test_id',
        storyTitle: 'Test Title',
        storyImageUrl: 'test_image.jpg',
        categoryId: 'test_category',
        timestamp: DateTime.now(),
        type: UserInteractionType.like,
      );

      expect(interaction.storyId, equals('test_id'));
      expect(interaction.storyTitle, equals('Test Title'));
      expect(interaction.type, equals(UserInteractionType.like));
    });

    test('should serialize and deserialize correctly', () {
      final originalInteraction = UserInteraction(
        storyId: 'test_id',
        storyTitle: 'Test Title',
        storyImageUrl: 'test_image.jpg',
        categoryId: 'test_category',
        timestamp: DateTime.now(),
        type: UserInteractionType.favorite,
      );

      final json = originalInteraction.toJson();
      final deserializedInteraction = UserInteraction.fromJson(json);

      expect(deserializedInteraction.storyId, equals(originalInteraction.storyId));
      expect(deserializedInteraction.storyTitle, equals(originalInteraction.storyTitle));
      expect(deserializedInteraction.type, equals(originalInteraction.type));
      expect(deserializedInteraction.timestamp, equals(originalInteraction.timestamp));
    });

    test('should handle equality correctly', () {
      final interaction1 = UserInteraction(
        storyId: 'test_id',
        storyTitle: 'Test Title',
        storyImageUrl: 'test_image.jpg',
        categoryId: 'test_category',
        timestamp: DateTime.now(),
        type: UserInteractionType.like,
      );

      final interaction2 = UserInteraction(
        storyId: 'test_id',
        storyTitle: 'Different Title',
        storyImageUrl: 'different_image.jpg',
        categoryId: 'different_category',
        timestamp: DateTime.now().add(const Duration(hours: 1)),
        type: UserInteractionType.like,
      );

      final interaction3 = UserInteraction(
        storyId: 'different_id',
        storyTitle: 'Test Title',
        storyImageUrl: 'test_image.jpg',
        categoryId: 'test_category',
        timestamp: DateTime.now(),
        type: UserInteractionType.like,
      );

      expect(interaction1, equals(interaction2)); // 相同storyId和type
      expect(interaction1, isNot(equals(interaction3))); // 不同storyId
    });
  });
}
