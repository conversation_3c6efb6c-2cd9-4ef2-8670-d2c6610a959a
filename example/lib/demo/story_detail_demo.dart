import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import '../pages/story_detail_page.dart';
import '../models/story.dart';
import '../models/story_category.dart';

/// 故事详情页面演示
/// 这个演示展示了故事详情页面的所有功能：
/// 1. 故事封面图片和标题
/// 2. 故事分类和时长信息
/// 3. 收藏和点赞功能
/// 4. 故事人物展示（包含用户头像）
/// 5. 故事背景设定
/// 6. 开始故事按钮
class StoryDetailDemo extends StatelessWidget {
  const StoryDetailDemo({super.key});

  @override
  Widget build(BuildContext context) {
    // 创建测试数据
    final testCategories = [
      StoryCategory(
        id: 'suspense',
        name: '悬疑',
        icon: Icons.psychology,
        color: Colors.purple,
      ),
      StoryCategory(
        id: 'scifi',
        name: '科幻',
        icon: Icons.rocket_launch,
        color: Colors.blue,
      ),
      StoryCategory(
        id: 'fantasy',
        name: '奇幻',
        icon: Icons.auto_awesome,
        color: Colors.green,
      ),
    ];

    final testStory = Story(
      id: 'demo_story',
      title: {
        'zh': '神秘的古堡',
        'en': 'The Mysterious Castle',
        'ar': 'القلعة الغامضة',
        'hi': 'रहस्यमय महल'
      },
      backgroundSetting: {
        'zh': '在一个风雨交加的夜晚，你来到了一座古老的城堡前。城堡高耸入云，四周被浓雾笼罩，显得格外神秘。传说这座城堡里隐藏着一个古老的秘密，只有勇敢的探险者才能揭开真相。',
        'en': 'On a stormy night, you arrive at an ancient castle. The castle towers into the clouds, surrounded by thick fog, appearing particularly mysterious. Legend has it that this castle hides an ancient secret that only brave explorers can uncover.',
        'ar': 'في ليلة عاصفة، تصل إلى قلعة قديمة. ترتفع القلعة إلى السحاب، محاطة بضباب كثيف، تبدو غامضة بشكل خاص. تقول الأسطورة أن هذه القلعة تخفي سرًا قديمًا لا يمكن للمستكشفين الشجعان فقط كشفه.',
        'hi': 'एक तूफानी रात में, आप एक प्राचीन महल पहुंचते हैं। महल बादलों में ऊंचा है, घने कोहरे से घिरा हुआ है, विशेष रूप से रहस्यमय दिखाई दे रहा है। किंवदंती है कि यह महल एक प्राचीन रहस्य छुपाता है जिसे केवल बहादुर खोजकर्ता ही उजागर कर सकते हैं।'
      },
      characterSetting: {
        'zh': '你是一位经验丰富的探险家，拥有敏锐的观察力和勇敢的心。在这次冒险中，你将面临各种挑战和谜题，需要运用你的智慧和勇气来解决问题。',
        'en': 'You are an experienced explorer with keen observation skills and a brave heart. In this adventure, you will face various challenges and puzzles, needing to use your wisdom and courage to solve problems.',
        'ar': 'أنت مستكشف ذو خبرة مع مهارات ملاحظة حادة وقلب شجاع. في هذه المغامرة، ستواجه تحديات وألغاز مختلفة، تحتاج إلى استخدام حكمتك وشجاعتك لحل المشاكل.',
        'hi': 'आप एक अनुभवी खोजकर्ता हैं जिसके पास तीक्ष्ण अवलोकन कौशल और बहादुर दिल है। इस साहसिक कार्य में, आप विभिन्न चुनौतियों और पहेलियों का सामना करेंगे, समस्याओं को हल करने के लिए अपनी बुद्धि और साहस का उपयोग करने की आवश्यकता होगी।'
      },
      currentPlot: {
        'zh': '当你推开城堡的大门时，一阵冷风扑面而来。大厅里点着几支蜡烛，微弱的光线照亮了古老的石墙。你注意到墙上挂着一幅神秘的画像，画中人的眼睛似乎在注视着你...',
        'en': 'As you push open the castle door, a cold wind hits your face. Several candles are lit in the hall, their weak light illuminating the ancient stone walls. You notice a mysterious portrait hanging on the wall, and the eyes in the painting seem to be watching you...',
        'ar': 'عندما تدفع باب القلعة، تضربك رياح باردة في وجهك. عدة شموع مضاءة في القاعة، ضوءها الضعيف ينير الجدران الحجرية القديمة. تلاحظ صورة غامضة معلقة على الحائط، والعيون في اللوحة تبدو وكأنها تراقبك...',
        'hi': 'जब आप महल का दरवाजा धकेलते हैं, तो एक ठंडी हवा आपके चेहरे से टकराती है। हॉल में कई मोमबत्तियां जली हुई हैं, उनकी कमजोर रोशनी प्राचीन पत्थर की दीवारों को रोशन कर रही है। आप दीवार पर लटकी एक रहस्यमय तस्वीर देखते हैं, और पेंटिंग में आंखें आपको देख रही लगती हैं...'
      },
      imageUrl: 'assets/images/stories/castle.jpg',
      categoryId: 'suspense',
      popularity: 95,
    );

    return MaterialApp(
      title: '故事详情页面演示',
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('zh'),
        Locale('en'),
        Locale('ar'),
        Locale('hi'),
      ],
      home: StoryDetailPage(
        story: testStory,
        categories: testCategories,
      ),
    );
  }
}

void main() {
  runApp(const StoryDetailDemo());
}
