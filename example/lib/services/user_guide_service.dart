import 'package:shared_preferences/shared_preferences.dart';

/// 用户引导服务，管理引导状态的持久化
class UserGuideService {
  static const String _guideCompletedKey = 'user_guide_completed';
  static UserGuideService? _instance;
  
  static UserGuideService get instance {
    _instance ??= UserGuideService._();
    return _instance!;
  }
  
  UserGuideService._();
  
  /// 检查用户是否已完成引导
  Future<bool> isGuideCompleted() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_guideCompletedKey) ?? false;
  }
  
  /// 标记引导为已完成
  Future<void> markGuideCompleted() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_guideCompletedKey, true);
  }
  
  /// 重置引导状态（用于测试）
  Future<void> resetGuideStatus() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_guideCompletedKey);
  }
}
