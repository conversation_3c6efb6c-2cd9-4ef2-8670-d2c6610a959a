import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_interaction.dart';
import '../models/story.dart';

/// 用户交互服务类
/// 管理用户的点赞和收藏数据持久化
class UserInteractionService {
  static const String _likesKey = 'user_likes';
  static const String _favoritesKey = 'user_favorites';
  static UserInteractionService? _instance;
  
  SharedPreferences? _prefs;
  List<UserInteraction> _likes = [];
  List<UserInteraction> _favorites = [];

  UserInteractionService._internal();

  static Future<UserInteractionService> getInstance() async {
    _instance ??= UserInteractionService._internal();
    await _instance!._initialize();
    return _instance!;
  }

  Future<void> _initialize() async {
    if (_prefs == null) {
      _prefs = await SharedPreferences.getInstance();
      await _loadData();
    }
  }

  /// 加载本地存储的数据
  Future<void> _loadData() async {
    try {
      // 加载点赞数据
      final likesData = _prefs!.getString(_likesKey);
      if (likesData != null) {
        final likesJson = jsonDecode(likesData) as List<dynamic>;
        _likes = likesJson
            .map((json) => UserInteraction.fromJson(json as Map<String, dynamic>))
            .toList();
      }

      // 加载收藏数据
      final favoritesData = _prefs!.getString(_favoritesKey);
      if (favoritesData != null) {
        final favoritesJson = jsonDecode(favoritesData) as List<dynamic>;
        _favorites = favoritesJson
            .map((json) => UserInteraction.fromJson(json as Map<String, dynamic>))
            .toList();
      }
    } catch (e) {
      print('加载用户交互数据失败: $e');
      _likes = [];
      _favorites = [];
    }
  }

  /// 保存点赞数据
  Future<void> _saveLikes() async {
    try {
      final likesJson = _likes.map((like) => like.toJson()).toList();
      await _prefs!.setString(_likesKey, jsonEncode(likesJson));
    } catch (e) {
      throw Exception('保存点赞数据失败: $e');
    }
  }

  /// 保存收藏数据
  Future<void> _saveFavorites() async {
    try {
      final favoritesJson = _favorites.map((favorite) => favorite.toJson()).toList();
      await _prefs!.setString(_favoritesKey, jsonEncode(favoritesJson));
    } catch (e) {
      throw Exception('保存收藏数据失败: $e');
    }
  }

  /// 点赞故事
  Future<void> likeStory(Story story, String locale) async {
    final storyTitle = _getLocalizedString(story.title, locale);
    final interaction = UserInteraction(
      storyId: story.id,
      storyTitle: storyTitle,
      storyImageUrl: story.imageUrl,
      categoryId: story.categoryId,
      timestamp: DateTime.now(),
      type: UserInteractionType.like,
    );

    _likes.removeWhere((like) => like.storyId == story.id);
    _likes.insert(0, interaction); // 插入到列表开头，最新的在前面
    await _saveLikes();
  }

  /// 取消点赞故事
  Future<void> unlikeStory(String storyId) async {
    _likes.removeWhere((like) => like.storyId == storyId);
    await _saveLikes();
  }

  /// 收藏故事
  Future<void> favoriteStory(Story story, String locale) async {
    final storyTitle = _getLocalizedString(story.title, locale);
    final interaction = UserInteraction(
      storyId: story.id,
      storyTitle: storyTitle,
      storyImageUrl: story.imageUrl,
      categoryId: story.categoryId,
      timestamp: DateTime.now(),
      type: UserInteractionType.favorite,
    );

    _favorites.removeWhere((favorite) => favorite.storyId == story.id);
    _favorites.insert(0, interaction); // 插入到列表开头，最新的在前面
    await _saveFavorites();
  }

  /// 取消收藏故事
  Future<void> unfavoriteStory(String storyId) async {
    _favorites.removeWhere((favorite) => favorite.storyId == storyId);
    await _saveFavorites();
  }

  /// 检查故事是否已点赞
  bool isStoryLiked(String storyId) {
    return _likes.any((like) => like.storyId == storyId);
  }

  /// 检查故事是否已收藏
  bool isStoryFavorited(String storyId) {
    return _favorites.any((favorite) => favorite.storyId == storyId);
  }

  /// 获取所有点赞的故事
  List<UserInteraction> getLikedStories() {
    return List.unmodifiable(_likes);
  }

  /// 获取所有收藏的故事
  List<UserInteraction> getFavoriteStories() {
    return List.unmodifiable(_favorites);
  }

  /// 获取点赞数量
  int getLikesCount() {
    return _likes.length;
  }

  /// 获取收藏数量
  int getFavoritesCount() {
    return _favorites.length;
  }

  /// 清空所有点赞
  Future<void> clearAllLikes() async {
    _likes.clear();
    await _saveLikes();
  }

  /// 清空所有收藏
  Future<void> clearAllFavorites() async {
    _favorites.clear();
    await _saveFavorites();
  }

  /// 获取本地化字符串
  String _getLocalizedString(Map<String, String> localizedMap, String locale) {
    return localizedMap[locale] ?? localizedMap['zh'] ?? localizedMap['en'] ?? '';
  }
}
