import 'package:flutter/material.dart';

/// 引导步骤类型
enum GuideStepType {
  categorySelection,  // 故事分类选择
  storyList,         // 故事列表点击
  storyDetail,       // 故事详情页滑动
  startStory,        // 开始故事按钮
  chatInteraction,   // 聊天页面交互
}

/// 手指动画类型
enum FingerAnimationType {
  tap,    // 点击
  swipeUp, // 向上滑动
}

/// 引导步骤数据模型
class GuideStep {
  final GuideStepType type;
  final String title;
  final String description;
  final FingerAnimationType animationType;
  final Rect? highlightRect; // 高亮区域
  final Offset? fingerPosition; // 手指动画位置
  
  const GuideStep({
    required this.type,
    required this.title,
    required this.description,
    required this.animationType,
    this.highlightRect,
    this.fingerPosition,
  });
}

/// 引导状态
enum GuideState {
  hidden,     // 隐藏
  showing,    // 显示中
  guiding,    // 引导中
  completed,  // 已完成
}
