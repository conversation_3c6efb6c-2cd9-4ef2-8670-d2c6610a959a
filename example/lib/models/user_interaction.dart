/// 用户交互数据模型
/// 用于存储用户的点赞和收藏操作
class UserInteraction {
  final String storyId;
  final String storyTitle;
  final String storyImageUrl;
  final String categoryId;
  final DateTime timestamp;
  final UserInteractionType type;

  UserInteraction({
    required this.storyId,
    required this.storyTitle,
    required this.storyImageUrl,
    required this.categoryId,
    required this.timestamp,
    required this.type,
  });

  Map<String, dynamic> toJson() {
    return {
      'storyId': storyId,
      'storyTitle': storyTitle,
      'storyImageUrl': storyImageUrl,
      'categoryId': categoryId,
      'timestamp': timestamp.toIso8601String(),
      'type': type.toString(),
    };
  }

  factory UserInteraction.fromJson(Map<String, dynamic> json) {
    return UserInteraction(
      storyId: json['storyId'] as String,
      storyTitle: json['storyTitle'] as String,
      storyImageUrl: json['storyImageUrl'] as String,
      categoryId: json['categoryId'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      type: UserInteractionType.values.firstWhere(
        (e) => e.toString() == json['type'],
      ),
    );
  }

  UserInteraction copyWith({
    String? storyId,
    String? storyTitle,
    String? storyImageUrl,
    String? categoryId,
    DateTime? timestamp,
    UserInteractionType? type,
  }) {
    return UserInteraction(
      storyId: storyId ?? this.storyId,
      storyTitle: storyTitle ?? this.storyTitle,
      storyImageUrl: storyImageUrl ?? this.storyImageUrl,
      categoryId: categoryId ?? this.categoryId,
      timestamp: timestamp ?? this.timestamp,
      type: type ?? this.type,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserInteraction &&
        other.storyId == storyId &&
        other.type == type;
  }

  @override
  int get hashCode => storyId.hashCode ^ type.hashCode;

  @override
  String toString() {
    return 'UserInteraction(storyId: $storyId, type: $type, timestamp: $timestamp)';
  }
}

/// 用户交互类型枚举
enum UserInteractionType {
  like,     // 点赞
  favorite, // 收藏
}

extension UserInteractionTypeExtension on UserInteractionType {
  String get displayName {
    switch (this) {
      case UserInteractionType.like:
        return '点赞';
      case UserInteractionType.favorite:
        return '收藏';
    }
  }

  String get iconName {
    switch (this) {
      case UserInteractionType.like:
        return 'thumb_up';
      case UserInteractionType.favorite:
        return 'favorite';
    }
  }
}
