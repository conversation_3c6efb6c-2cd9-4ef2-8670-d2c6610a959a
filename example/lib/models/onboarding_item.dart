class OnboardingItem {
  final String imagePath;
  final String titleKey;
  final String descriptionKey;

  const OnboardingItem({
    required this.imagePath,
    required this.titleKey,
    required this.descriptionKey,
  });

  static List<OnboardingItem> get items => [
    const OnboardingItem(
      imagePath: 'assets/images/onboarding_1.webp',
      titleKey: 'onboarding_title_1',
      descriptionKey: 'onboarding_description_1',
    ),
    const OnboardingItem(
      imagePath: 'assets/images/onboarding_2.webp',
      titleKey: 'onboarding_title_2',
      descriptionKey: 'onboarding_description_2',
    ),
    const OnboardingItem(
      imagePath: 'assets/images/onboarding_3.webp',
      titleKey: 'onboarding_title_3',
      descriptionKey: 'onboarding_description_3',
    ),
  ];
}
