import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../models/onboarding_item.dart';
import '../services/onboarding_service.dart';
import '../ui/design_spec.dart';

class OnboardingPage extends StatefulWidget {
  final VoidCallback onCompleted;

  const OnboardingPage({
    super.key,
    required this.onCompleted,
  });

  @override
  State<OnboardingPage> createState() => _OnboardingPageState();
}

class _OnboardingPageState extends State<OnboardingPage> {
  final PageController _pageController = PageController();
  int _currentIndex = 0;
  final List<OnboardingItem> _items = OnboardingItem.items;

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  void _nextPage() {
    if (_currentIndex < _items.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _completeOnboarding();
    }
  }

  void _completeOnboarding() async {
    await OnboardingService.setOnboardingCompleted();
    widget.onCompleted();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    
    return Scaffold(
      backgroundColor: DesignSpec.primaryBackground,
      body: SafeArea(
        child: Column(
          children: [
            // Skip button
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Align(
                alignment: Alignment.topRight,
                child: TextButton(
                  onPressed: _completeOnboarding,
                  child: Text(
                    localizations.skip ?? 'Skip',
                    style: TextStyle(
                      color: DesignSpec.primaryItemUnselected,
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
            ),
            
            // PageView content
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: _onPageChanged,
                itemCount: _items.length,
                itemBuilder: (context, index) {
                  return _buildOnboardingItem(context, _items[index]);
                },
              ),
            ),
            
            // Page indicator and button
            Padding(
              padding: const EdgeInsets.all(32.0),
              child: Column(
                children: [
                  // Page indicator
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(
                      _items.length,
                      (index) => Container(
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        width: _currentIndex == index ? 24 : 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: _currentIndex == index
                              ? DesignSpec.primaryItemSelected
                              : DesignSpec.primaryItemUnselected.withOpacity(0.3),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 32),
                  
                  // Continue/Explore button
                  SizedBox(
                    width: double.infinity,
                    height: 56,
                    child: ElevatedButton(
                      onPressed: _nextPage,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: DesignSpec.primaryItemSelected,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(28),
                        ),
                        elevation: 0,
                      ),
                      child: Text(
                        _currentIndex == _items.length - 1
                            ? (localizations.explore ?? 'Explore')
                            : (localizations.continueText ?? 'Continue'),
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOnboardingItem(BuildContext context, OnboardingItem item) {
    final localizations = AppLocalizations.of(context)!;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Image
          Expanded(
            flex: 4,
            child: Container(
              width: double.infinity,
              margin: const EdgeInsets.only(bottom: 20),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(20),
                child: Image.asset(
                  item.imagePath,
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),

          // Title and description
          Expanded(
            flex: 2,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  _getLocalizedText(localizations, item.titleKey),
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: DesignSpec.primaryText,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),

                const SizedBox(height: 12),

                Expanded(
                  child: Text(
                    _getLocalizedText(localizations, item.descriptionKey),
                    style: TextStyle(
                      fontSize: 16,
                      color: DesignSpec.primaryText.withOpacity(0.7),
                      height: 1.3,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 4,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getLocalizedText(AppLocalizations localizations, String key) {
    switch (key) {
      case 'onboarding_title_1':
        return localizations.onboardingTitle1 ?? '故事类型丰富';
      case 'onboarding_description_1':
        return localizations.onboardingDescription1 ?? '探索各种精彩的文字冒险故事，从奇幻冒险到现代都市，总有一款适合你';
      case 'onboarding_title_2':
        return localizations.onboardingTitle2 ?? '选择决定命运';
      case 'onboarding_description_2':
        return localizations.onboardingDescription2 ?? '每个选择都会影响故事的走向，体验不同的剧情发展和结局';
      case 'onboarding_title_3':
        return localizations.onboardingTitle3 ?? '自由对话互动';
      case 'onboarding_description_3':
        return localizations.onboardingDescription3 ?? '与故事角色自由对话，引导剧情发展，创造属于你的独特故事';
      default:
        return key;
    }
  }
}
