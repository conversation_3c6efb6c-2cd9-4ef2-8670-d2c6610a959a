import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../models/user_interaction.dart';
import '../models/story.dart';
import '../models/story_category.dart';
import '../services/user_interaction_service.dart';
import '../ui/design_spec.dart';
import 'story_detail_page.dart';
import '../widgets/app_dialogs.dart';

class LikedStoriesPage extends StatefulWidget {
  final List<StoryCategory> categories;

  const LikedStoriesPage({
    super.key,
    required this.categories,
  });

  @override
  State<LikedStoriesPage> createState() => _LikedStoriesPageState();
}

class _LikedStoriesPageState extends State<LikedStoriesPage> {
  List<UserInteraction> _likedStories = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadLikedStories();
  }

  Future<void> _loadLikedStories() async {
    try {
      final service = await UserInteractionService.getInstance();
      setState(() {
        _likedStories = service.getLikedStories();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _likedStories = [];
        _isLoading = false;
      });
    }
  }

  Future<void> _removeLike(UserInteraction interaction) async {
    final localizations = AppLocalizations.of(context)!;
    
    final confirmed = await AppDialogs.showConfirmDialog(
      context: context,
      title: localizations.removeLike,
      content: localizations.confirmRemoveLike,
      confirmText: localizations.confirm,
      cancelText: localizations.cancel,
      isDestructive: true,
    );

    if (confirmed == true) {
      try {
        final service = await UserInteractionService.getInstance();
        await service.unlikeStory(interaction.storyId);
        
        setState(() {
          _likedStories.removeWhere((item) => item.storyId == interaction.storyId);
        });
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(localizations.operationSuccess),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${localizations.operationFailed}: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _navigateToStoryDetail(UserInteraction interaction) {
    // 创建一个临时的Story对象用于导航
    final story = Story(
      id: interaction.storyId,
      title: {'zh': interaction.storyTitle, 'en': interaction.storyTitle},
      backgroundSetting: {'zh': '', 'en': ''},
      characterSetting: {'zh': '', 'en': ''},
      currentPlot: {'zh': '', 'en': ''},
      imageUrl: interaction.storyImageUrl,
      categoryId: interaction.categoryId,
      popularity: 0,
    );

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StoryDetailPage(
          story: story,
          categories: widget.categories,
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        return '${difference.inMinutes}分钟前';
      }
      return '${difference.inHours}小时前';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else {
      return '${date.month}/${date.day}';
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: DesignSpec.primaryBackground,
      appBar: AppBar(
        backgroundColor: DesignSpec.secondaryBackground,
        elevation: 1,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black87),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          localizations.likedStories,
          style: const TextStyle(
            color: Colors.black87,
            fontSize: DesignSpec.fontSizeLg,
            fontWeight: DesignSpec.fontWeightSemiBold,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _likedStories.isEmpty
              ? _buildEmptyState(localizations)
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _likedStories.length,
                  itemBuilder: (context, index) {
                    final interaction = _likedStories[index];
                    return _buildLikedStoryItem(interaction, localizations);
                  },
                ),
    );
  }

  Widget _buildEmptyState(AppLocalizations localizations) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.thumb_up_outlined,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 24),
          Text(
            localizations.noLikedStories,
            style: TextStyle(
              fontSize: DesignSpec.fontSizeLg,
              color: Colors.grey[600],
              fontWeight: DesignSpec.fontWeightMedium,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: ElevatedButton.styleFrom(
              backgroundColor: DesignSpec.primaryItemSelected,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(24),
              ),
            ),
            child: Text(localizations.startExploring),
          ),
        ],
      ),
    );
  }

  Widget _buildLikedStoryItem(UserInteraction interaction, AppLocalizations localizations) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: DesignSpec.secondaryBackground,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _navigateToStoryDetail(interaction),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // 故事封面
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.asset(
                  interaction.storyImageUrl,
                  width: 60,
                  height: 60,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      width: 60,
                      height: 60,
                      color: Colors.grey[300],
                      child: const Icon(
                        Icons.image_not_supported,
                        color: Colors.grey,
                        size: 24,
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(width: 16),
              
              // 故事信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      interaction.storyTitle,
                      style: const TextStyle(
                        fontSize: DesignSpec.fontSizeBase,
                        fontWeight: DesignSpec.fontWeightSemiBold,
                        color: DesignSpec.primaryText,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          Icons.thumb_up,
                          size: 16,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${localizations.likedOn} ${_formatDate(interaction.timestamp)}',
                          style: TextStyle(
                            fontSize: DesignSpec.fontSizeSm,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              // 取消点赞按钮
              IconButton(
                onPressed: () => _removeLike(interaction),
                icon: const Icon(
                  Icons.thumb_up,
                  color: Colors.red,
                  size: 24,
                ),
                tooltip: localizations.removeLike,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
