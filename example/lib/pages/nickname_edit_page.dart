import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../ui/design_spec.dart';
import '../services/user_profile_service.dart';

class NicknameEditPage extends StatefulWidget {
  const NicknameEditPage({super.key});

  @override
  State<NicknameEditPage> createState() => _NicknameEditPageState();
}

class _NicknameEditPageState extends State<NicknameEditPage> {
  final TextEditingController _nicknameController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  
  UserProfileService? _profileService;
  String _originalNickname = '';
  bool _isLoading = true;
  bool _isSaving = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  @override
  void dispose() {
    _nicknameController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  Future<void> _initializeData() async {
    try {
      _profileService = await UserProfileService.getInstance();
      final currentProfile = _profileService!.getCurrentProfile();
      _originalNickname = currentProfile.nickname;
      _nicknameController.text = _originalNickname;
    } catch (e) {
      debugPrint('初始化昵称数据失败: $e');
      _originalNickname = 'User';
      _nicknameController.text = _originalNickname;
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        // 自动聚焦到输入框
        _focusNode.requestFocus();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignSpec.primaryBackground,
      appBar: AppBar(
        title: Text(AppLocalizations.of(context)!.editNickname),
        backgroundColor: DesignSpec.primaryBackground,
        elevation: 0,
        foregroundColor: DesignSpec.primaryText,
        actions: [
          if (_isSaving)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            )
          else
            TextButton(
              onPressed: _hasChanges() && _isValidNickname() ? _saveNickname : null,
              child: Text(
                AppLocalizations.of(context)!.save,
                style: TextStyle(
                  color: _hasChanges() && _isValidNickname()
                      ? DesignSpec.primaryItemSelected 
                      : DesignSpec.primaryItemUnselected,
                  fontWeight: DesignSpec.fontWeightMedium,
                ),
              ),
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildContent(),
    );
  }

  Widget _buildContent() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInstructionSection(),
            const SizedBox(height: 30),
            _buildInputSection(),
            const SizedBox(height: 20),
            if (_errorMessage != null) _buildErrorMessage(),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructionSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: DesignSpec.secondaryBackground,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(
            Icons.person_outline,
            size: 32,
            color: DesignSpec.primaryItemSelected,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppLocalizations.of(context)!.nicknamePersonalizationHint,
                  style: TextStyle(
                    fontSize: DesignSpec.fontSizeSm,
                    color: DesignSpec.primaryItemUnselected,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInputSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: DesignSpec.secondaryBackground,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context)!.enterNickname,
            style: TextStyle(
              fontSize: DesignSpec.fontSizeBase,
              fontWeight: DesignSpec.fontWeightMedium,
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _nicknameController,
            focusNode: _focusNode,
            maxLength: 20,
            decoration: InputDecoration(
              hintText: AppLocalizations.of(context)!.enterNickname,
              hintStyle: TextStyle(
                color: DesignSpec.primaryItemUnselected.withOpacity(0.6),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: DesignSpec.primaryItemUnselected.withOpacity(0.3),
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: DesignSpec.primaryItemSelected,
                  width: 2,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(
                  color: Colors.red,
                  width: 2,
                ),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(
                  color: Colors.red,
                  width: 2,
                ),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
              counterStyle: TextStyle(
                color: DesignSpec.primaryItemUnselected.withOpacity(0.6),
                fontSize: DesignSpec.fontSizeXs,
              ),
            ),
            style: TextStyle(
              fontSize: DesignSpec.fontSizeBase,
            ),
            onChanged: (value) {
              setState(() {
                _errorMessage = null;
              });
            },
            onSubmitted: (value) {
              if (_hasChanges() && _isValidNickname()) {
                _saveNickname();
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildErrorMessage() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.red.withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              _errorMessage!,
              style: TextStyle(
                color: Colors.red,
                fontSize: DesignSpec.fontSizeSm,
              ),
            ),
          ),
        ],
      ),
    );
  }

  bool _hasChanges() {
    return _nicknameController.text.trim() != _originalNickname;
  }

  bool _isValidNickname() {
    final nickname = _nicknameController.text.trim();
    return nickname.isNotEmpty && nickname.length <= 20;
  }

  Future<void> _saveNickname() async {
    final nickname = _nicknameController.text.trim();
    
    if (nickname.isEmpty) {
      setState(() {
        _errorMessage = AppLocalizations.of(context)!.nicknameCannotBeEmpty;
      });
      return;
    }

    if (nickname.length > 20) {
      setState(() {
        _errorMessage = AppLocalizations.of(context)!.nicknameLengthError;
      });
      return;
    }

    setState(() {
      _isSaving = true;
      _errorMessage = null;
    });

    try {
      await _profileService!.updateNickname(nickname);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.profileSaved),
            backgroundColor: DesignSpec.primaryItemSelected,
          ),
        );
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = AppLocalizations.of(context)!.profileSaveFailed;
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.profileSaveFailed),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }
}
