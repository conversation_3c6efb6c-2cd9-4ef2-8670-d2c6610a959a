import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../ui/design_spec.dart';
import '../services/user_profile_service.dart';

class AvatarEditPage extends StatefulWidget {
  const AvatarEditPage({super.key});

  @override
  State<AvatarEditPage> createState() => _AvatarEditPageState();
}

class _AvatarEditPageState extends State<AvatarEditPage> {
  UserProfileService? _profileService;
  String? _selectedAvatarPath;
  String? _currentAvatarPath;
  List<String> _maleAvatars = [];
  List<String> _femaleAvatars = [];
  bool _isLoading = true;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  Future<void> _initializeData() async {
    try {
      _profileService = await UserProfileService.getInstance();
      final currentProfile = _profileService!.getCurrentProfile();
      _currentAvatarPath = currentProfile.avatarPath;
      _selectedAvatarPath = currentProfile.avatarPath;
      _maleAvatars = _profileService!.getMaleAvatars();
      _femaleAvatars = _profileService!.getFemaleAvatars();
    } catch (e) {
      debugPrint('初始化头像数据失败: $e');
      _maleAvatars = [];
      _femaleAvatars = [];
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignSpec.primaryBackground,
      appBar: AppBar(
        title: Text(AppLocalizations.of(context)!.editAvatar),
        backgroundColor: DesignSpec.primaryBackground,
        elevation: 0,
        foregroundColor: DesignSpec.primaryText,
        actions: [
          if (_isSaving)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            )
          else
            TextButton(
              onPressed: _hasChanges() ? _saveAvatar : null,
              child: Text(
                AppLocalizations.of(context)!.save,
                style: TextStyle(
                  color: _hasChanges() 
                      ? DesignSpec.primaryItemSelected 
                      : DesignSpec.primaryItemUnselected,
                  fontWeight: DesignSpec.fontWeightMedium,
                ),
              ),
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildContent(),
    );
  }

  Widget _buildContent() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCurrentAvatarSection(),
            const SizedBox(height: 20),
            Expanded(
              child: _buildAvatarCategoriesSection(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentAvatarSection() {
    // 使用更安全的布局结构，避免Expanded导致的布局计算冲突
    return Container(
      child: Row(
        children: [
          Expanded(child: Hero(
            tag: 'avatar_preview',
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              width: 200,
              height: 250,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                image: _selectedAvatarPath?.isNotEmpty == true
                    ? DecorationImage(
                  image: AssetImage(_selectedAvatarPath!),
                  fit: BoxFit.cover,
                )
                    : null,
                color: _selectedAvatarPath?.isEmpty != false
                    ? DesignSpec.primaryItemUnselected.withOpacity(0.2)
                    : null,
                border: Border.all(
                  color: DesignSpec.primaryItemSelected,
                  width: 3,
                ),
              ),
              child: _selectedAvatarPath?.isEmpty != false
                  ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.person,
                      size: 64,
                      color: DesignSpec.primaryItemUnselected,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      AppLocalizations.of(context)!.selectAvatarHint,
                      style: TextStyle(
                        fontSize: DesignSpec.fontSizeBase,
                        color: DesignSpec.primaryItemUnselected,
                      ),
                    ),
                  ],
                ),
              )
                  : null,
            ),
          )),
        ],
      ),
    );
  }

  Widget _buildAvatarCategoriesSection() {
    return DefaultTabController(
      length: 2,
      child: Column(
        children: [
          Container(
            decoration: BoxDecoration(
              color: DesignSpec.secondaryBackground,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TabBar(
              labelColor: DesignSpec.primaryItemSelected,
              unselectedLabelColor: DesignSpec.primaryItemUnselected,
              indicatorColor: DesignSpec.primaryItemSelected,
              indicatorWeight: 3,
              tabs: [
                Tab(
                  icon: Icon(Icons.male),
                  text: AppLocalizations.of(context)!.maleAvatars,
                ),
                Tab(
                  icon: Icon(Icons.female),
                  text: AppLocalizations.of(context)!.femaleAvatars,
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: TabBarView(
              children: [
                _buildAvatarGrid(_maleAvatars),
                _buildAvatarGrid(_femaleAvatars),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyAvatarsPlaceholder() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.photo_library_outlined,
            size: 64,
            color: DesignSpec.primaryItemUnselected.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            AppLocalizations.of(context)!.avatarResourcesNotAdded,
            style: TextStyle(
              fontSize: DesignSpec.fontSizeBase,
              color: DesignSpec.primaryItemUnselected,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            AppLocalizations.of(context)!.avatarResourcesHint,
            style: TextStyle(
              fontSize: DesignSpec.fontSizeSm,
              color: DesignSpec.primaryItemUnselected.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAvatarGrid(List<String> avatars) {
    if (avatars.isEmpty) {
      return _buildEmptyAvatarsPlaceholder();
    }

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: DesignSpec.secondaryBackground,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1,
        ),
        itemCount: avatars.length,
        itemBuilder: (context, index) {
          final avatarPath = avatars[index];
          final isSelected = avatarPath == _selectedAvatarPath;

          return GestureDetector(
            onTap: () => _selectAvatar(avatarPath),
            child: Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected
                      ? DesignSpec.primaryItemSelected
                      : Colors.transparent,
                  width: 3,
                ),
              ),
              child: Container(
                margin: const EdgeInsets.all(3),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: DesignSpec.primaryItemUnselected.withOpacity(0.1),
                ),
                child: ClipOval(
                  child: Image.asset(
                    avatarPath,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: DesignSpec.primaryItemUnselected.withOpacity(0.2),
                        child: Icon(
                          Icons.person,
                          size: 32,
                          color: DesignSpec.primaryItemUnselected,
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  void _selectAvatar(String avatarPath) {
    setState(() {
      _selectedAvatarPath = avatarPath;
    });
  }

  bool _hasChanges() {
    return _selectedAvatarPath != _currentAvatarPath;
  }

  Future<void> _saveAvatar() async {
    if (!_hasChanges() || _selectedAvatarPath == null) return;

    setState(() {
      _isSaving = true;
    });

    try {
      await _profileService!.updateAvatarAndGender(_selectedAvatarPath!);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.profileSaved),
            backgroundColor: DesignSpec.primaryItemSelected,
          ),
        );
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.profileSaveFailed),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }
}
