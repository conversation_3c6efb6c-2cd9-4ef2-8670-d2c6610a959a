import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../models/chat_session.dart';
import '../models/story.dart';
import '../services/chat_session_manager.dart';
import '../services/story_data_service.dart';
import '../pages/chat_page.dart';
import '../ui/design_spec.dart';

class ChatSessionListPage extends StatefulWidget {
  const ChatSessionListPage({super.key});

  @override
  State<ChatSessionListPage> createState() => _ChatSessionListPageState();
}

class _ChatSessionListPageState extends State<ChatSessionListPage> {
  List<ChatSession> _sessions = [];
  bool _isLoading = true;
  bool _isSelectionMode = false;
  final Set<String> _selectedSessionIds = {};

  @override
  void initState() {
    super.initState();
    _loadSessions();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _loadStories();
  }

  @override
  void didUpdateWidget(ChatSessionListPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    setState(() {});
  }

  Future<void> _loadSessions() async {
    try {
      final sessions = await ChatSessionManager.instance.getAllSessions();
      setState(() {
        _sessions = sessions;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadStories() async {
    await StoryDataService.instance.loadStories();
    _migrateSessionData();
  }

  Future<void> _migrateSessionData() async {
    bool hasChanges = false;
    
    for (final session in _sessions) {
      if (session.storyId == null && session.id.startsWith('story_')) {
        final storyId = _extractStoryIdFromSessionId(session.id);
        if (storyId != null) {
          final updatedSession = session.copyWith(storyId: storyId);
          await ChatSessionManager.instance.updateSession(updatedSession);
          hasChanges = true;
        }
      }
    }
    
    if (hasChanges) {
      _loadSessions();
    }
  }

  String? _extractStoryIdFromSessionId(String sessionId) {
    if (sessionId.startsWith('story_')) {
      final parts = sessionId.split('_');
      if (parts.length >= 2) {
        return parts[1];
      }
    }
    return null;
  }

  void _enterSelectionMode() {
    setState(() {
      _isSelectionMode = true;
      _selectedSessionIds.clear();
    });
  }

  void _exitSelectionMode() {
    setState(() {
      _isSelectionMode = false;
      _selectedSessionIds.clear();
    });
  }

  void _toggleSessionSelection(String sessionId) {
    setState(() {
      if (_selectedSessionIds.contains(sessionId)) {
        _selectedSessionIds.remove(sessionId);
      } else {
        _selectedSessionIds.add(sessionId);
      }
    });
  }

  Future<void> _deleteSelectedSessions() async {
    if (_selectedSessionIds.isEmpty) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocalizations.of(context)!.deleteSelected),
        content: Text(AppLocalizations.of(context)!.deleteSelectedConfirmation(_selectedSessionIds.length)),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(AppLocalizations.of(context)!.cancel),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text(AppLocalizations.of(context)!.delete),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        for (final sessionId in _selectedSessionIds) {
          await ChatSessionManager.instance.deleteSession(sessionId);
        }
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(AppLocalizations.of(context)!.deleteSuccess)),
          );
        }
        
        _exitSelectionMode();
        _loadSessions();
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(AppLocalizations.of(context)!.deleteFailed)),
          );
        }
      }
    }
  }

  
  double _getImageWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) return screenWidth * 0.3;
    if (screenWidth < 600) return screenWidth * 0.35;
    return screenWidth * 0.4;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignSpec.primaryBackground,
      appBar: AppBar(
        backgroundColor: DesignSpec.secondaryBackground,
        elevation: 1,
        title: Text(_isSelectionMode
            ? AppLocalizations.of(context)!.sessionsSelected(_selectedSessionIds.length)
            : AppLocalizations.of(context)!.chatSessions),
        actions: [
          if (_sessions.isNotEmpty)
            IconButton(
              icon: Icon(
                Icons.delete_sweep,
                color: _isSelectionMode ? Colors.red : null,
              ),
              onPressed: _isSelectionMode ? _exitSelectionMode : _enterSelectionMode,
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _sessions.isEmpty
              ? _buildEmptyState()
              : Column(
                  children: [
                    if (_isSelectionMode) _buildSelectionActionBar(),
                    Expanded(child: _buildSessionList()),
                  ],
                ),
    );
  }

  Widget _buildSelectionActionBar() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: DesignSpec.secondaryBackground,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        // mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Expanded(
            child: TextButton(
              onPressed: _exitSelectionMode,
              child: Text(AppLocalizations.of(context)!.cancelSelection),
            ),
          ),
          Container(
            color: Colors.grey[300],
            width: 1,
            height: 24,
          ),
          Expanded(
            child: TextButton(
              onPressed: _selectedSessionIds.isNotEmpty ? _deleteSelectedSessions : null,
              style: ElevatedButton.styleFrom(
                foregroundColor: _selectedSessionIds.isNotEmpty ? Colors.red : Colors.white,
              ),
              child: Text(AppLocalizations.of(context)!.deleteSelected),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            AppLocalizations.of(context)!.noChatSessionsHint,
            style: TextStyle(
              fontSize: DesignSpec.fontSizeBase,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSessionList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: _sessions.length,
      itemBuilder: (context, index) {
        final session = _sessions[index];
        return _buildSessionItem(session);
      },
    );
  }

  Widget _buildSessionItem(ChatSession session) {
    String displayTitle = session.title;
    Story? story;
    
    if (session.storyId != null) {
      displayTitle = StoryDataService.instance.getStoryTitleFromContext(session.storyId!, context);
      story = StoryDataService.instance.getStoryById(session.storyId!);
    }
    
    final isSelected = _selectedSessionIds.contains(session.id);
    
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isSelected
            ? BorderSide(color: Colors.red, width: 2)
            : BorderSide.none,
      ),
      child: Stack(
        children: [
          // 背景图片和渐变遮罩
          if (story != null)
            Positioned.fill(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Stack(
                  children: [
                    // 背景图片
                    Positioned(
                      left: 0,
                      right: 0,
                      top: 0,
                      bottom: 0,
                      child: SizedBox(
                        width: _getImageWidth(context),
                        child: Image.asset(
                          story.imageUrl,
                          fit: BoxFit.cover,
                          // placeholder: (context, url) => Container(
                          //   color: Colors.grey[200],
                          // ),
                          // errorWidget: (context, url, error) => const SizedBox(),
                        ),
                      ),
                    ),
                    // 渐变遮罩
                    Positioned.fill(
                      child: DecoratedBox(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                            // stops: const [0.0, 0.4, 0.7, 1.0],
                            colors: [
                              Theme.of(context).cardColor,
                              Theme.of(context).cardColor.withOpacity(0.95),
                              Theme.of(context).cardColor.withOpacity(0.9),
                              Theme.of(context).cardColor.withOpacity(0.5),
                              Colors.transparent,
                              // Colors.black.withValues(alpha: 0.2),
                              // Colors.black.withValues(alpha: 0.4),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          // 内容层
          ListTile(
            contentPadding: const EdgeInsets.all(16),
            leading: _isSelectionMode
                ? Checkbox(
                    value: isSelected,
                    onChanged: (value) {
                      _toggleSessionSelection(session.id);
                    },
                    activeColor: Colors.red,
                  )
                : CircleAvatar(
                    backgroundColor: DesignSpec.primaryItemSelected.withValues(alpha: 0.1),
                    child: const Icon(Icons.auto_stories, color: DesignSpec.primaryItemSelected),
                  ),
            title: Text(
              displayTitle.isNotEmpty ? displayTitle : session.title,
              style: TextStyle(
                fontWeight: DesignSpec.fontWeightSemiBold,
                fontSize: DesignSpec.fontSizeBase,
                color: Theme.of(context).textTheme.titleMedium?.color,
                shadows: [
                  Shadow(
                    offset: const Offset(0, 1),
                    blurRadius: 3,
                    color: Colors.black.withValues(alpha: story != null ? 0.3 : 0),
                  ),
                ],
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 4),
                Text(
                  '${session.messages.length} ${AppLocalizations.of(context)!.messages}',
                  style: TextStyle(
                    fontSize: DesignSpec.fontSizeSm,
                    color: Theme.of(context).textTheme.bodySmall?.color,
                    shadows: [
                      Shadow(
                        offset: const Offset(0, 1),
                        blurRadius: 2,
                        color: Colors.black.withValues(alpha: story != null ? 0.2 : 0),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  session.formattedTime,
                  style: TextStyle(
                    fontSize: DesignSpec.fontSizeXs,
                    color: Theme.of(context).textTheme.bodySmall?.color?.withValues(alpha: 0.8),
                    shadows: [
                      Shadow(
                        offset: const Offset(0, 1),
                        blurRadius: 2,
                        color: Colors.black.withValues(alpha: story != null ? 0.2 : 0),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            trailing: null,
            onTap: _isSelectionMode
                ? () {
                    _toggleSessionSelection(session.id);
                  }
                : () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => ChatPage(session: session),
                      ),
                    );
                  },
            onLongPress: () {
              if (!_isSelectionMode) {
                _enterSelectionMode();
                _toggleSessionSelection(session.id);
              }
            },
          ),
        ],
      ),
    );
  }
}
