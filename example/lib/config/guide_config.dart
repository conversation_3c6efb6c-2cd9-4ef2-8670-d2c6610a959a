import 'package:flutter/material.dart';
import '../models/guide_step.dart';

/// 引导配置类
class GuideConfig {
  /// 引导步骤配置
  static const List<GuideStep> steps = [
    GuideStep(
      type: GuideStepType.categorySelection,
      title: '选择故事分类',
      description: '点击不同的故事分类标签，探索各种类型的精彩故事',
      animationType: FingerAnimationType.tap,
    ),
    GuideStep(
      type: GuideStepType.storyList,
      title: '浏览故事列表',
      description: '在这里可以看到该分类下的所有故事，点击任意故事卡片进入详情',
      animationType: FingerAnimationType.tap,
    ),
    GuideStep(
      type: GuideStepType.storyDetail,
      title: '查看故事详情',
      description: '向上滑动可以查看完整的故事背景、角色设定和当前剧情',
      animationType: FingerAnimationType.swipeUp,
    ),
    GuideStep(
      type: GuideStepType.startStory,
      title: '开始你的冒险',
      description: '准备好了吗？点击"开始故事"按钮，开启你的互动冒险之旅',
      animationType: FingerAnimationType.tap,
    ),
    GuideStep(
      type: GuideStepType.chatInteraction,
      title: '与故事互动',
      description: '你可以选择预设的对话选项，或者在输入框中自由输入。右侧的加载按钮表示AI正在为你生成精彩的剧情发展',
      animationType: FingerAnimationType.tap,
    ),
  ];
  
  /// 根据屏幕尺寸动态计算高亮区域
  static Rect getHighlightRect(GuideStepType type, Size screenSize) {
    switch (type) {
      case GuideStepType.categorySelection:
        // 故事分类区域 - 相对于屏幕顶部的位置
        return Rect.fromLTWH(
          24, 
          screenSize.height * 0.25, 
          screenSize.width - 48, 
          60
        );
        
      case GuideStepType.storyList:
        // 故事列表区域 - 水平滚动列表
        return Rect.fromLTWH(
          24, 
          screenSize.height * 0.4, 
          screenSize.width - 48, 
          200
        );
        
      case GuideStepType.storyDetail:
        // 故事详情页面 - 整个内容区域
        return Rect.fromLTWH(
          0, 
          screenSize.height * 0.2, 
          screenSize.width, 
          screenSize.height * 0.6
        );
        
      case GuideStepType.startStory:
        // 开始故事按钮 - 页面底部
        return Rect.fromLTWH(
          50, 
          screenSize.height * 0.8, 
          screenSize.width - 100, 
          50
        );
        
      case GuideStepType.chatInteraction:
        // 聊天输入区域 - 屏幕底部
        return Rect.fromLTWH(
          20, 
          screenSize.height * 0.85, 
          screenSize.width - 40, 
          60
        );
    }
  }
  
  /// 根据高亮区域计算手指动画位置
  static Offset getFingerPosition(Rect highlightRect, FingerAnimationType type) {
    switch (type) {
      case FingerAnimationType.tap:
        // 点击动画位置在高亮区域中心
        return Offset(
          highlightRect.center.dx - 20, 
          highlightRect.center.dy - 20
        );
        
      case FingerAnimationType.swipeUp:
        // 向上滑动动画位置在高亮区域下方
        return Offset(
          highlightRect.center.dx - 20, 
          highlightRect.bottom - 40
        );
    }
  }
  
  /// 引导角色配置
  static const String guideCharacterImage = 'assets/images/guide.webp';
  static const String defaultBubbleText = '不知道怎么玩吗？';
  
  /// 动画配置
  static const Duration slideAnimationDuration = Duration(milliseconds: 800);
  static const Duration bubbleAnimationDuration = Duration(milliseconds: 300);
  static const Duration fingerAnimationDuration = Duration(milliseconds: 1500);
  
  /// 颜色配置
  static const Color overlayColor = Colors.black54;
  static const Color highlightBorderColor = Colors.white;
  static const Color bubbleBackgroundColor = Colors.white;
  static const Color tipBackgroundColor = Colors.white;
}
