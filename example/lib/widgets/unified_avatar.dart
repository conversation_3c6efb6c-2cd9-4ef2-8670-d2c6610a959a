import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../services/user_profile_service.dart';

class UnifiedAvatar extends StatelessWidget {
  final double radius;
  final bool showBorder;
  final Color? borderColor;

  const UnifiedAvatar({
    super.key,
    this.radius = 20.0,
    this.showBorder = false,
    this.borderColor,
  });

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: UserProfileService.getInstance(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return CircleAvatar(
            radius: radius,
            backgroundColor: Colors.grey[300],
            child: const CircularProgressIndicator(strokeWidth: 2),
          );
        }

        if (snapshot.hasError || !snapshot.hasData) {
          return CircleAvatar(
            radius: radius,
            backgroundColor: Colors.grey[300],
            child: Icon(Icons.person, size: radius, color: Colors.grey[600]),
          );
        }

        final userProfileService = snapshot.data!;
        final currentProfile = userProfileService.getCurrentProfile();
        final avatarUrl = currentProfile?.avatarPath;

        return CircleAvatar(
          radius: radius,
          backgroundColor: Colors.grey[300],
          backgroundImage: avatarUrl != null && avatarUrl.isNotEmpty
              ? CachedNetworkImageProvider(avatarUrl)
              : null,
          child: (avatarUrl == null || avatarUrl.isEmpty)
              ? Icon(Icons.person, size: radius, color: Colors.grey[600])
              : null,
        );
      },
    );
  }
}