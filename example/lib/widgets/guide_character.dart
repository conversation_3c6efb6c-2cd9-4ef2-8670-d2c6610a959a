import 'package:flutter/material.dart';
import '../config/guide_config.dart';

/// 引导角色组件
class GuideCharacter extends StatefulWidget {
  final bool isVisible;
  final VoidCallback? onTap;
  final String bubbleText;
  final bool showBubble;

  const GuideCharacter({
    super.key,
    required this.isVisible,
    this.onTap,
    this.bubbleText = GuideConfig.defaultBubbleText,
    this.showBubble = true,
  });

  @override
  State<GuideCharacter> createState() => _GuideCharacterState();
}

class _GuideCharacterState extends State<GuideCharacter>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _bubbleController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _bubbleAnimation;

  @override
  void initState() {
    super.initState();

    // 滑入滑出动画控制器
    _slideController = AnimationController(
      duration: GuideConfig.slideAnimationDuration,
      vsync: this,
    );

    // 气泡动画控制器
    _bubbleController = AnimationController(
      duration: GuideConfig.bubbleAnimationDuration,
      vsync: this,
    );

    // 滑动动画：从右侧滑入
    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    // 气泡缩放动画
    _bubbleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _bubbleController,
      curve: Curves.easeOut,
    ));

    // 首帧后根据可见性触发入场动画（用于数据加载完成后首次展示）
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;
      if (widget.isVisible) {
        _slideIn();
      } else {
        _slideController.value = 0.0;
        _bubbleController.value = 0.0;
      }
    });
  }



  @override
  void didUpdateWidget(GuideCharacter oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.isVisible != oldWidget.isVisible) {
      if (widget.isVisible) {
        _slideIn();
      } else {
        _slideOut();
      }
    }

    if (widget.showBubble != oldWidget.showBubble) {
      if (widget.showBubble) {
        _showBubble();
      } else {
        _hideBubble();
      }
    }
  }

  void _slideIn() {
    _slideController.forward().then((_) {
      if (widget.showBubble) {
        _showBubble();
      }
    });
  }

  void _slideOut() {
    _hideBubble();
    _slideController.reverse();
  }

  void _showBubble() {
    _bubbleController.forward();
  }

  void _hideBubble() {
    _bubbleController.reverse();
  }

  @override
  void dispose() {
    _slideController.dispose();
    _bubbleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      bottom: 220,
      right: 0,
      child: SlideTransition(
        position: _slideAnimation,
        child: GestureDetector(
          onTap: widget.onTap,
          child: Stack(
            clipBehavior: Clip.none,
            children: [
              // 气泡提示
              if (widget.showBubble)
                Positioned(
                  bottom: 130,
                  right: 20,
                  child: ScaleTransition(
                    scale: _bubbleAnimation,
                    child: _buildBubble(),
                  ),
                ),

              // 角色图片
              _buildCharacter(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBubble() {
    return Container(
      constraints: const BoxConstraints(maxWidth: 150),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: GuideConfig.bubbleBackgroundColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 6),
            child: Text(
              widget.bubbleText,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.black87,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          // 气泡尖角
          Positioned(
            bottom: -6,
            right: 20,
            child: CustomPaint(
              size: const Size(12, 6),
              painter: _BubbleTailPainter(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCharacter() {
    return Container(
      width: 80,
      height: 150,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(40),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Image.asset(
        GuideConfig.guideCharacterImage,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          // 如果图片加载失败，显示默认图标
          return Container(
            decoration: BoxDecoration(
              color: Colors.blue.shade100,
              borderRadius: BorderRadius.circular(40),
            ),
            child: const Icon(
              Icons.help_outline,
              size: 40,
              color: Colors.blue,
            ),
          );
        },
      ),
    );
  }
}

/// 气泡尖角绘制器
class _BubbleTailPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    final path = Path();
    path.moveTo(0, 0);
    path.lineTo(size.width / 2, size.height);
    path.lineTo(size.width, 0);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
