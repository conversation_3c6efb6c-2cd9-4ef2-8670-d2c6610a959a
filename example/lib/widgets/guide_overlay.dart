import 'package:flutter/material.dart';
import '../models/guide_step.dart';

/// 引导遮罩层组件
class GuideOverlay extends StatelessWidget {
  final GuideStep step;
  final VoidCallback? onSkip;
  final VoidCallback? onNext;
  final bool showSkipButton;
  
  const GuideOverlay({
    super.key,
    required this.step,
    this.onSkip,
    this.onNext,
    this.showSkipButton = true,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Stack(
        children: [
          // 黑色半透明遮罩
          Container(
            width: double.infinity,
            height: double.infinity,
            color: Colors.black.withOpacity(0.7),
          ),
          
          // 镂空高亮区域
          if (step.highlightRect != null)
            _buildHighlightArea(step.highlightRect!),
          
          // 手指动画
          if (step.fingerPosition != null)
            _buildFingerAnimation(),
          
          // 提示文案
          _buildTipContent(context),
          
          // 跳过按钮
          if (showSkipButton)
            _buildSkipButton(context),
        ],
      ),
    );
  }
  
  Widget _buildHighlightArea(Rect rect) {
    return Positioned(
      left: rect.left,
      top: rect.top,
      width: rect.width,
      height: rect.height,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.transparent,
          border: Border.all(
            color: Colors.white,
            width: 2,
          ),
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.white.withOpacity(0.3),
              blurRadius: 10,
              spreadRadius: 2,
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildFingerAnimation() {
    return Positioned(
      left: step.fingerPosition!.dx,
      top: step.fingerPosition!.dy,
      child: _FingerAnimation(type: step.animationType),
    );
  }
  
  Widget _buildTipContent(BuildContext context) {
    return Positioned(
      bottom: 120,
      left: 24,
      right: 24,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              step.title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              step.description,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black54,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildSkipButton(BuildContext context) {
    return Positioned(
      top: MediaQuery.of(context).padding.top + 16,
      right: 24,
      child: GestureDetector(
        onTap: onSkip,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.9),
            borderRadius: BorderRadius.circular(20),
          ),
          child: const Text(
            '跳过',
            style: TextStyle(
              fontSize: 14,
              color: Colors.black87,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }
}

/// 手指动画组件
class _FingerAnimation extends StatefulWidget {
  final FingerAnimationType type;
  
  const _FingerAnimation({required this.type});

  @override
  State<_FingerAnimation> createState() => _FingerAnimationState();
}

class _FingerAnimationState extends State<_FingerAnimation>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Transform.translate(
          offset: _getAnimationOffset(),
          child: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.9),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Icon(
              _getFingerIcon(),
              color: Colors.black87,
              size: 20,
            ),
          ),
        );
      },
    );
  }
  
  Offset _getAnimationOffset() {
    switch (widget.type) {
      case FingerAnimationType.tap:
        // 点击动画：轻微的缩放效果
        final scale = 1.0 + (_animation.value * 0.1);
        return Offset(0, (1 - scale) * 20);
      case FingerAnimationType.swipeUp:
        // 向上滑动动画
        return Offset(0, -_animation.value * 50);
    }
  }
  
  IconData _getFingerIcon() {
    switch (widget.type) {
      case FingerAnimationType.tap:
        return Icons.touch_app;
      case FingerAnimationType.swipeUp:
        return Icons.keyboard_arrow_up;
    }
  }
}
