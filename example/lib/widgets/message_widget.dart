import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../models/message.dart';
import '../ui/design_spec.dart';
import 'image_preview_dialog.dart';

class MessageWidget extends StatelessWidget {
  final Message message;
  final VoidCallback? onImageTap;
  final Function(Message, MessageChoice)? onChoiceSelected;

  const MessageWidget({
    super.key,
    required this.message,
    this.onImageTap,
    this.onChoiceSelected,
  });

  @override
  Widget build(BuildContext context) {
    switch (message.type) {
      case MessageType.plot:
        return _buildSlotMessage();
      case MessageType.image:
        return _buildImageMessage(context);
      case MessageType.text:
        return _buildTextMessage();
      case MessageType.system:
        return _buildSystemMessage();
      case MessageType.choices:
        return _buildChoicesMessage();
    }
  }

  Widget _buildSystemMessage() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: Center(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            message.content,
            style: TextStyle(
              fontSize: DesignSpec.fontSizeXs,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  Widget _buildSlotMessage() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: DesignSpec.secondaryBackground,
        borderRadius: BorderRadius.circular(18),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Text(
        message.content,
        style: const TextStyle(
          fontSize: DesignSpec.fontSizeSm,
          color: Colors.black87,
          height: 1.4,
        ),
      ),
    );
  }

  Widget _buildImageMessage(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: Column(
        children: [
          // 图片部分
          GestureDetector(
            onTap: () => _showImagePreview(context),
            child: Container(
              constraints: const BoxConstraints(
                maxWidth: 300,
                maxHeight: 200,
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: CachedNetworkImage(
                  imageUrl: message.imageUrl!,
                  fit: BoxFit.cover,
                  width: double.infinity,
                  height: double.infinity,
                  placeholder: (context, url) => Container(
                    height: 150,
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Center(
                      child: CircularProgressIndicator(),
                    ),
                  ),
                  errorWidget: (context, url, error) => Container(
                    height: 150,
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Center(
                      child: Icon(
                        Icons.broken_image,
                        color: Colors.grey,
                        size: 48,
                      ),
                    ),
                  ),
                  fadeInDuration: const Duration(milliseconds: 300),
                  cacheManager: null,
                  useOldImageOnUrlChange: true,
                  maxHeightDiskCache: 500,
                  memCacheWidth: 250,
                  memCacheHeight: 200,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextMessage() {
    final isUser = message.sender == MessageSender.user;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
      child: Row(
        mainAxisAlignment: isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (!isUser) ...[
            CircleAvatar(
              radius: 16,
              backgroundColor: DesignSpec.primaryItemSelected.withOpacity(0.1),
              child: Icon(
                Icons.smart_toy,
                size: 16,
                color: DesignSpec.primaryItemSelected,
              ),
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: isUser
                    ? DesignSpec.primaryItemSelected
                    : DesignSpec.secondaryBackground,
                borderRadius: BorderRadius.only(
                  topLeft: const Radius.circular(18),
                  topRight: const Radius.circular(18),
                  bottomLeft: Radius.circular(isUser ? 18 : 4),
                  bottomRight: Radius.circular(isUser ? 4 : 18),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 4,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Text(
                message.content,
                style: TextStyle(
                  fontSize: DesignSpec.fontSizeSm,
                  color: isUser ? Colors.white : Colors.black87,
                  height: 1.4,
                ),
              ),
            ),
          ),
          if (isUser) ...[
            const SizedBox(width: 8),
            CircleAvatar(
              radius: 16,
              backgroundColor: Colors.grey[300],
              child: Icon(
                Icons.person,
                size: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildChoicesMessage() {
    if (message.choices == null || message.choices!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 提示文本
          if (message.content.isNotEmpty)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              margin: const EdgeInsets.only(bottom: 12),
              decoration: BoxDecoration(
                color: DesignSpec.secondaryBackground,
                borderRadius: BorderRadius.circular(18),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 4,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Text(
                message.content,
                style: const TextStyle(
                  fontSize: DesignSpec.fontSizeSm,
                  color: Colors.black87,
                  height: 1.4,
                ),
              ),
            ),
          // 选项按钮
          ...message.choices!.asMap().entries.map((entry) {
            final index = entry.key;
            final choice = entry.value;
            final isSelected = choice.isSelected;
            final hasAnySelection = message.choices!.any((c) => c.isSelected);
            final isDisabled = hasAnySelection && !isSelected;

            return Container(
              margin: const EdgeInsets.only(bottom: 8),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: isDisabled ? null : () {
                    if (onChoiceSelected != null) {
                      onChoiceSelected!(message, choice);
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: isSelected
                        ? DesignSpec.primaryItemSelected
                        : isDisabled
                            ? Colors.grey[300]
                            : Colors.white,
                    foregroundColor: isSelected
                        ? Colors.white
                        : isDisabled
                            ? Colors.grey[600]
                            : Colors.black87,
                    elevation: isDisabled ? 0 : 2,
                    shadowColor: Colors.black.withOpacity(0.1),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                      side: BorderSide(
                        color: isSelected
                            ? DesignSpec.primaryItemSelected
                            : isDisabled
                                ? Colors.grey[300]!
                                : Colors.grey[300]!,
                        width: 1,
                      ),
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  ),
                  child: Row(
                    children: [
                      Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          color: isSelected
                              ? Colors.white
                              : isDisabled
                                  ? Colors.grey[400]
                                  : DesignSpec.primaryItemSelected,
                          shape: BoxShape.circle,
                        ),
                        child: Center(
                          child: Text(
                            '${index + 1}',
                            style: TextStyle(
                              color: isSelected
                                  ? DesignSpec.primaryItemSelected
                                  : Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          choice.text,
                          style: TextStyle(
                            fontSize: DesignSpec.fontSizeSm,
                            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                          ),
                        ),
                      ),
                      if (isSelected)
                        const Icon(
                          Icons.check_circle,
                          color: Colors.white,
                          size: 20,
                        ),
                    ],
                  ),
                ),
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  void _showImagePreview(BuildContext context) {
    if (message.imageUrl != null) {
      showDialog(
        context: context,
        builder: (context) => ImagePreviewDialog(
          imageUrl: message.imageUrl!,
          description: message.content,
        ),
      );
    }
  }
}