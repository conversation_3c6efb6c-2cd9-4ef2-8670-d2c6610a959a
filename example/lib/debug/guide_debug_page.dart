import 'package:flutter/material.dart';
import '../services/guide_manager.dart';
import '../services/user_guide_service.dart';
import '../models/guide_step.dart';

/// 引导系统调试页面
class GuideDebugPage extends StatefulWidget {
  const GuideDebugPage({super.key});

  @override
  State<GuideDebugPage> createState() => _GuideDebugPageState();
}

class _GuideDebugPageState extends State<GuideDebugPage> {
  bool _isGuideCompleted = false;
  GuideState _currentState = GuideState.hidden;
  int _currentStepIndex = 0;

  @override
  void initState() {
    super.initState();
    _loadGuideStatus();
  }

  void _loadGuideStatus() async {
    final isCompleted = await UserGuideService.instance.isGuideCompleted();
    setState(() {
      _isGuideCompleted = isCompleted;
      _currentState = GuideManager.instance.currentState;
      _currentStepIndex = GuideManager.instance.currentStepIndex;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('引导系统调试'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildStatusSection(),
            const SizedBox(height: 24),
            _buildControlSection(),
            const SizedBox(height: 24),
            _buildStepsSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '引导状态',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            _buildStatusRow('引导完成状态', _isGuideCompleted ? '已完成' : '未完成'),
            _buildStatusRow('当前状态', _getStateText(_currentState)),
            _buildStatusRow('当前步骤', '${_currentStepIndex + 1}/5'),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontWeight: FontWeight.w500)),
          Text(value, style: const TextStyle(color: Colors.blue)),
        ],
      ),
    );
  }

  String _getStateText(GuideState state) {
    switch (state) {
      case GuideState.hidden:
        return '隐藏';
      case GuideState.showing:
        return '显示中';
      case GuideState.guiding:
        return '引导中';
      case GuideState.completed:
        return '已完成';
    }
  }

  Widget _buildControlSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '控制操作',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton(
                  onPressed: _startGuide,
                  child: const Text('开始引导'),
                ),
                ElevatedButton(
                  onPressed: _resetGuide,
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
                  child: const Text('重置引导'),
                ),
                ElevatedButton(
                  onPressed: _completeGuide,
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
                  child: const Text('完成引导'),
                ),
                ElevatedButton(
                  onPressed: _refreshStatus,
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.grey),
                  child: const Text('刷新状态'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStepsSection() {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '引导步骤',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 12),
              Expanded(
                child: ListView.builder(
                  itemCount: 5,
                  itemBuilder: (context, index) {
                    return _buildStepItem(index);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStepItem(int index) {
    final stepTypes = [
      GuideStepType.categorySelection,
      GuideStepType.storyList,
      GuideStepType.storyDetail,
      GuideStepType.startStory,
      GuideStepType.chatInteraction,
    ];
    
    final stepTitles = [
      '选择故事分类',
      '浏览故事列表',
      '查看故事详情',
      '开始故事',
      '互动聊天',
    ];

    final isCurrentStep = index == _currentStepIndex;
    final isCompleted = index < _currentStepIndex || _isGuideCompleted;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isCurrentStep 
            ? Colors.blue.withOpacity(0.1)
            : isCompleted 
                ? Colors.green.withOpacity(0.1)
                : Colors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isCurrentStep 
              ? Colors.blue
              : isCompleted 
                  ? Colors.green
                  : Colors.grey,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: isCurrentStep 
                  ? Colors.blue
                  : isCompleted 
                      ? Colors.green
                      : Colors.grey,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                '${index + 1}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  stepTitles[index],
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    color: isCurrentStep ? Colors.blue : Colors.black87,
                  ),
                ),
                Text(
                  stepTypes[index].toString().split('.').last,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
          if (isCurrentStep)
            const Icon(Icons.play_arrow, color: Colors.blue)
          else if (isCompleted)
            const Icon(Icons.check, color: Colors.green),
        ],
      ),
    );
  }

  void _startGuide() {
    GuideManager.instance.startGuide(context);
    _refreshStatus();
  }

  void _resetGuide() async {
    await GuideManager.instance.resetGuide();
    _refreshStatus();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('引导状态已重置')),
    );
  }

  void _completeGuide() {
    GuideManager.instance.completeGuide();
    _refreshStatus();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('引导已完成')),
    );
  }

  void _refreshStatus() {
    _loadGuideStatus();
  }
}
