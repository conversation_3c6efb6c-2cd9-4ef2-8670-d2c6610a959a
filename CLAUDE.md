# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个基于Flutter的聊天应用插件，具有以下特性：
- 多语言支持（英语、中文、阿拉伯语、印地语）
- 故事板聊天功能
- 用户个人资料管理
- 头像选择和编辑
- 与SiliconFlow API集成进行AI聊天和图像生成

## 开发命令

### 运行应用
```bash
# 进入示例目录
cd example

# 运行Flutter应用
flutter run

# 运行在特定设备上
flutter run -d <device_id>
```

### 构建和测试
```bash
# 构建APK
flutter build apk

# 构建iOS
flutter build ios

# 运行测试
flutter test

# 运行特定测试文件
flutter test test/<test_file>.dart
```

### 代码分析和格式化
```bash
# 代码格式化
flutter format .

# 静态代码分析
flutter analyze

# 生成本地化文件
flutter gen-l10n
```

### 依赖管理
```bash
# 获取依赖
flutter pub get

# 升级依赖
flutter pub upgrade

# 检查过时的依赖
flutter pub outdated
```

## 项目架构

### 目录结构
```
lib/
├── data/           # 数据相关
│   └── system_prompt.dart
├── l10n/           # 本地化文件
│   ├── app_ar.arb
│   ├── app_en.arb
│   ├── app_hi.arb
│   └── app_zh.arb
├── models/         # 数据模型
│   ├── chat_session.dart
│   ├── message.dart
│   ├── plot.dart
│   ├── resp/       # API响应模型
│   │   ├── chat_completion_resp.dart
│   │   └── image_generation_resp.dart
│   ├── story.dart
│   ├── story_category.dart
│   └── user_profile.dart
├── network/        # 网络层
│   └── api.dart
├── pages/          # 页面组件
│   ├── avatar_edit_page.dart
│   ├── chat_page.dart
│   ├── chat_session_list_page.dart
│   ├── language_settings_page.dart
│   ├── nickname_edit_page.dart
│   ├── plot_test_page.dart
│   ├── profile_page.dart
│   ├── storyboard_page.dart
│   └── webview_page.dart
├── services/       # 服务层
│   ├── chat_session_manager.dart
│   ├── chat_session_storage.dart
│   ├── retry_service.dart
│   └── user_profile_service.dart
├── test_data/      # 测试数据
│   └── sample_plot_responses.dart
├── ui/             # UI设计规范
│   └── design_spec.dart
├── utils/          # 工具类
│   ├── error_logger.dart
│   ├── plot_parser.dart
│   ├── screen_util.dart
│   └── story_chat_adapter.dart
└── widgets/        # 自定义组件
    ├── arc_clipper.dart
    ├── image_preview_dialog.dart
    ├── message_widget.dart
    └── story_category_chip.dart
```

### 核心组件

1. **数据模型 (Models)**
   - `ChatSession`: 聊天会话管理
   - `Message`: 消息模型，支持多种消息类型
   - `UserProfile`: 用户配置文件
   - `Story`: 故事内容模型

2. **网络层 (Network)**
   - `Api`: 与SiliconFlow API的集成
   - 支持聊天完成和图像生成功能
   - 内置重试机制和错误处理

3. **服务层 (Services)**
   - `ChatSessionManager`: 聊天会话管理
   - `UserProfileService`: 用户配置服务
   - `RetryService`: API重试服务

4. **UI组件 (Pages/Widgets)**
   - 基于Tab导航的三页面架构
   - 故事板页面、聊天列表页面、个人资料页面
   - 自定义消息组件和图像预览对话框

## API集成

项目使用SiliconFlow API进行AI聊天和图像生成：
- 聊天完成端点: `/v1/chat/completions`
- 图像生成端点: `/v1/images/generations`
- 模型: Qwen/Qwen2.5-7B-Instruct (聊天), Kwai-Kolors/Kolors (图像)

## 本地化支持

支持4种语言：
- 英语 (en)
- 中文 (zh) 
- 阿拉伯语 (ar)
- 印地语 (hi)

使用Flutter的本地化框架和arb文件管理多语言内容。

## 资产文件

静态资源位于 `example/assets/`:
- `avatars/`: 用户头像资源
- `images/`: 应用图片
- `data/stories.json`: 故事数据

## 开发注意事项

1. **API密钥管理**: API密钥硬编码在代码中，建议使用环境变量或安全存储
2. **错误处理**: 网络请求包含重试机制和错误验证
3. **状态管理**: 使用简单的setState进行状态管理，可考虑引入状态管理库
4. **性能优化**: 注意列表渲染性能和图像加载优化

## 测试策略

目前项目缺少自动化测试，建议添加：
- 单元测试: 模型类和服务类的测试
- 组件测试: UI组件的widget测试
- 集成测试: 完整的应用流程测试

## 扩展建议

1. 添加更完善的状态管理（如Provider、Bloc）
2. 实现离线消息存储和同步
3. 添加消息推送通知
4. 支持更多聊天模型和提供商
5. 实现消息加密和安全性增强